#!/usr/bin/env python3
"""
Test script for AI-based Resume Validation

This script tests the new OpenAI-based resume validation system
that quickly determines if a document is a resume without full text extraction.
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.services.resume_validator import ai_resume_validator
from app.core.config import settings

async def test_ai_validation():
    """Test the AI-based resume validation"""
    
    print("🤖 Testing AI-Based Resume Validation")
    print("=" * 50)
    
    # Check API keys
    print("1. Checking API Keys...")
    if not settings.OPENAI_API_KEY:
        print("❌ OPENAI_API_KEY not set")
        return False
    else:
        print("✅ OPENAI_API_KEY is configured")
    
    # Test with sample resume content
    sample_resume_content = """
    JOHN DOE
    Software Engineer
    
    Contact Information:
    Email: <EMAIL>
    Phone: (*************
    LinkedIn: linkedin.com/in/johndoe
    
    PROFESSIONAL SUMMARY
    Experienced software engineer with 5+ years in web development
    
    WORK EXPERIENCE
    Senior Software Developer | Tech Corp | 2020-2023
    • Developed web applications using React and Node.js
    • Improved application performance by 40%
    
    Software Developer | StartupXYZ | 2018-2020
    • Built REST APIs using Python and Django
    
    EDUCATION
    Bachelor of Science in Computer Science
    University of Technology | 2014-2018
    GPA: 3.8/4.0
    
    SKILLS
    Programming Languages: Python, JavaScript, Java
    Frameworks: React, Django, Node.js
    """
    
    # Test with non-resume content
    sample_non_resume_content = """
    TERMS OF SERVICE
    
    Last updated: January 1, 2024
    
    1. ACCEPTANCE OF TERMS
    By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement.
    
    2. PRIVACY POLICY
    Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service.
    
    3. USER ACCOUNTS
    When you create an account with us, you must provide information that is accurate, complete, and current at all times.
    
    4. PROHIBITED USES
    You may not use our Service for any unlawful purpose or to solicit others to perform unlawful acts.
    """
    
    try:
        # Test 1: Valid Resume
        print("\n2. Testing with Valid Resume Content...")
        resume_bytes = sample_resume_content.encode('utf-8')
        
        validation_result = await ai_resume_validator.validate_document_as_resume(
            resume_bytes, 
            "john_doe_resume.txt"
        )
        
        print("✅ Resume validation completed!")
        print(f"   Is Resume: {validation_result['is_resume']}")
        print(f"   Likelihood: {validation_result['likelihood']}%")
        print(f"   Confidence: {validation_result['confidence']}")
        print(f"   Reason: {validation_result['reason']}")
        
        # Test 2: Non-Resume Document
        print("\n3. Testing with Non-Resume Content...")
        non_resume_bytes = sample_non_resume_content.encode('utf-8')
        
        validation_result_2 = await ai_resume_validator.validate_document_as_resume(
            non_resume_bytes, 
            "terms_of_service.txt"
        )
        
        print("✅ Non-resume validation completed!")
        print(f"   Is Resume: {validation_result_2['is_resume']}")
        print(f"   Likelihood: {validation_result_2['likelihood']}%")
        print(f"   Confidence: {validation_result_2['confidence']}")
        print(f"   Reason: {validation_result_2['reason']}")
        
        # Test 3: Empty Document
        print("\n4. Testing with Empty Content...")
        empty_bytes = b""
        
        validation_result_3 = await ai_resume_validator.validate_document_as_resume(
            empty_bytes, 
            "empty_file.txt"
        )
        
        print("✅ Empty document validation completed!")
        print(f"   Is Resume: {validation_result_3['is_resume']}")
        print(f"   Likelihood: {validation_result_3['likelihood']}%")
        print(f"   Reason: {validation_result_3['reason']}")
        
        # Display results summary
        print("\n" + "=" * 50)
        print("📊 VALIDATION RESULTS SUMMARY")
        print("=" * 50)
        
        print(f"Resume Content:")
        print(f"  ✅ Correctly identified as resume: {validation_result['is_resume']}")
        print(f"  📊 Likelihood: {validation_result['likelihood']}%")
        print(f"  🎯 Confidence: {validation_result['confidence']}")
        
        print(f"\nNon-Resume Content:")
        print(f"  ❌ Correctly rejected as non-resume: {not validation_result_2['is_resume']}")
        print(f"  📊 Likelihood: {validation_result_2['likelihood']}%")
        print(f"  🎯 Confidence: {validation_result_2['confidence']}")
        
        print(f"\nEmpty Content:")
        print(f"  ❌ Correctly rejected as empty: {not validation_result_3['is_resume']}")
        print(f"  📊 Likelihood: {validation_result_3['likelihood']}%")
        
        # Check if results are as expected
        success = (
            validation_result['is_resume'] and 
            not validation_result_2['is_resume'] and 
            not validation_result_3['is_resume']
        )
        
        if success:
            print("\n✅ AI Resume Validation Test PASSED!")
            print("🚀 The system correctly identifies resumes vs non-resumes!")
        else:
            print("\n❌ AI Resume Validation Test FAILED!")
            print("⚠️  The system did not correctly classify all test cases.")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """Check if the environment is properly configured"""
    print("🔧 Environment Check")
    print("=" * 30)
    
    required_vars = [
        "OPENAI_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = getattr(settings, var, None)
        if value:
            print(f"✅ {var}: Configured")
        else:
            print(f"❌ {var}: Missing")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these variables before running the test.")
        return False
    
    return True

async def main():
    """Main test function"""
    print("🤖 AI Resume Validation Test")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        return
    
    # Run AI validation test
    success = await test_ai_validation()
    
    if success:
        print("\n🎉 All tests passed! AI validation is working correctly.")
        print("⚡ This should significantly speed up resume processing!")
    else:
        print("\n💥 Tests failed. Please check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
