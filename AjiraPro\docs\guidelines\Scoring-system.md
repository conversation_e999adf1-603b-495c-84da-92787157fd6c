# Resume Scoring Guidelines

## Purpose
This document outlines the scoring system for evaluating general resumes, providing users with actionable insights to improve their documents based on AI analysis. The scoring system is divided into two main parts:

- **Claude**: Evaluates the resume's format, structure, font, and formatting (25% of total score)
- **OpenAI**: Evaluates the content of the resume, including career overview, experience, education, certifications, additional qualifications, and content quality (75% of total score)

The overall score is calculated as a weighted average of the scores from Claude and OpenAI, ranging from 0 to 100%.

---

## Scoring Breakdown

### 1. <PERSON> (25% of total score)

<PERSON> focuses on the format and structure of the resume, ensuring it is clean, standard, and easily parsable by Applicant Tracking Systems (ATS).

#### Category: Resume Format & Structure

ATS systems prefer clean, standard formatting that can be easily parsed.

**Scoring Criteria:**

**High Score (90-100%):**
- Uses reverse chronological format (most recent experience first)
- Clear, standard section headings (e.g., "Work Experience," "Education")
- Simple, ATS-friendly fonts (e.g., Arial, Calibri, Times New Roman)
- No tables, columns, images, or graphics
- Saved in ATS-compatible file format (.docx or standard PDF)
- Consistent date formatting throughout (e.g., MM/YYYY)
- Well-used bullet points in experience sections, with a maximum of 2 lines per bullet
- Perfect use of whitespace and simple, ATS-friendly formatting
- Contains standard contact information in the text (not in header/footer)

**Low Score (10-30%):**
- Uses creative or functional format with non-standard sections
- Contains images, graphics, charts, or tables
- Uses multiple columns that confuse parsing algorithms
- Includes text boxes or non-standard formatting elements
- Uses uncommon fonts or excessive formatting (e.g., bold, italic, underline)
- Stored in incompatible file formats (e.g., .pages, image files)
- Poor or no use of bullet points in experience sections, or bullets exceed 2 lines
- Poor use of whitespace and not ATS-friendly formatting
- Critical information placed in headers or footers

---

### 2. OpenAI (75% of total score)

OpenAI evaluates the content of the resume across five categories, each contributing to the overall score based on their weight.

#### Categories and Weights:
- **Career Overview**: 25%
- **Experience**: 40%
- **Education and Certifications**: 15%
- **Additional Qualifications**: 10%
- **Content Quality**: 10%

#### Scoring Criteria for Each Category:

#### 2.1 Career Overview (25%)

**High Score (90-100%):**
- Professional summary is clear, concise, and effectively highlights key strengths, experience, and career goals in a way that appeals to a broad audience
- Skills section is comprehensive, well-organized, and includes relevant hard skills (e.g., technical proficiencies) and soft skills (e.g., communication, leadership)

**Medium Score (50-70%):**
- Summary provides some insight into the candidate's background but lacks specificity or impact
- Skills section includes a decent range of skills but may miss key competencies or lack organization

**Low Score (10-30%):**
- Summary is vague, overly lengthy, or missing, failing to provide a strong introduction to the candidate's profile
- Skills section is sparse, irrelevant to the candidate's career goals, or poorly presented

#### 2.2 Experience (40%)

**High Score (90-100%):**
- Work history is detailed, with clear descriptions of roles, responsibilities, and specific, quantifiable achievements (e.g., "Increased efficiency by 15%" or "Led a team of 5")
- Demonstrates career progression, with each role building on the previous one in terms of responsibility or expertise
- Experience showcases transferable skills applicable to a variety of roles

**Medium Score (50-70%):**
- Job descriptions include some details and achievements but lack consistency or quantification
- Career progression is evident but not strongly emphasized, or some roles lack relevance

**Low Score (10-30%):**
- Job descriptions are vague, brief, or focus only on duties without highlighting achievements or impact
- No clear career progression, or significant unexplained employment gaps exist

#### 2.3 Education and Certifications (15%)

**High Score (90-100%):**
- Relevant degrees, certifications, and professional training are clearly listed and aligned with the candidate's career field or goals
- Includes key details such as institutions, dates, and honors (if applicable)

**Medium Score (50-70%):**
- Education and certifications are listed but may include some irrelevant entries or lack detail

**Low Score (10-30%):**
- Key education or certification details are missing or irrelevant to the candidate's career
- Section is incomplete or poorly presented

#### 2.4 Additional Qualifications (10%)

**High Score (90-100%):**
- Includes relevant and valuable supplementary information (e.g., languages, volunteer work, professional affiliations) that enhances the candidate's profile
- Demonstrates well-roundedness or specialized expertise applicable to various roles

**Medium Score (50-70%):**
- Contains some additional qualifications, but they may not significantly strengthen the resume or be fully relevant

**Low Score (10-30%):**
- Contains irrelevant or outdated information that does not add value
- Missing key qualifications that could enhance the candidate's profile

#### 2.5 Content Quality (10%)

**High Score (90-100%):**
- Resume is written in clear, professional language with no grammatical or typographical errors
- Information is concise and logically presented, making it easy to understand

**Medium Score (50-70%):**
- Minor errors or inconsistencies in language exist, but they do not significantly detract from readability
- Writing is generally clear but could be more concise or polished

**Low Score (10-30%):**
- Contains frequent errors in grammar, spelling, or punctuation
- Writing is unclear, overly verbose, or unprofessional, reducing the resume's effectiveness

---

## Overall Score Calculation

The overall resume score is calculated as a weighted average:

- **Claude (25%)**: Score based on format and structure
- **OpenAI (75%)**: Score based on the weighted average of the five content categories

### Formula:
```
Overall Score = (0.25 × Claude Score) + (0.75 × OpenAI Score)
```

Where:
```
OpenAI Score = (0.25 × Career Overview) + (0.40 × Experience) + (0.15 × Education) + (0.10 × Additional Qualifications) + (0.10 × Content Quality)
```
**NOTE**: Resumes might have other sections that are not covered by the scoring system. In such cases, the score should be adjusted accordingly.

## Implementation Guidelines

### AI Model Integration
- **Claude**: Responsible for format and structure analysis
- **OpenAI**: Responsible for content evaluation and scoring
- Both models should provide detailed feedback for each scoring category

### Score Presentation
- Display overall score prominently (0-100%)
- Break down scores by category for transparency
- Provide specific improvement recommendations for each low-scoring area
- Use visual indicators (progress bars, color coding) to make scores easily digestible

### Quality Assurance
- Implement validation checks to ensure scores are within expected ranges
- Cross-reference AI evaluations for consistency
- Provide fallback scoring mechanisms in case of AI service unavailability

### User Experience Considerations
- Present scores in a way that motivates improvement rather than discourages
- Offer actionable next steps for each scoring category
- Allow users to understand how improvements would impact their overall score

## Technical Implementation Notes

### API Integration
- Separate API calls for Claude (format analysis) and OpenAI (content analysis)
- Implement proper error handling and timeout management
- Cache results to avoid redundant API calls for the same document

### Performance Optimization
- Process format and content analysis in parallel where possible
- Implement progressive loading for score results
- Use efficient document parsing to extract relevant sections for analysis

### Data Privacy
- Ensure resume content is handled securely during analysis
- Implement proper data retention policies
