"""
Resume Analysis Celery Tasks

Handles asynchronous processing of resume parsing and scoring
"""

import logging
import json
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from uuid import UUID

from celery import current_task
from ..celery_worker import celery
from ..services.supabase import supabase
from ..services.document_processor import document_processor
from ..services.claude_parser import claude_parser

logger = logging.getLogger(__name__)

@celery.task(bind=True, max_retries=3)
def parse_resume_task(self, resume_id: str, file_path: str):
    """
    Parse resume content using Claude Sonnet 3.5

    Args:
        resume_id: UUID of the resume record
        file_path: Path to the resume file in Supabase storage
    """
    try:
        logger.info(f"Starting resume parsing for resume_id: {resume_id}")

        # Update status to parsing
        supabase.table("resumes").update({
            "parsing_status": "parsing"
        }).eq("id", resume_id).execute()

        # Download file from Supabase storage
        logger.info(f"Downloading file from Supabase: {file_path}")
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise Exception(f"Failed to download file from Supabase: {file_path}")

        # Extract text from document
        logger.info(f"Extracting text from document: {file_path}")
        filename = file_path.split('/')[-1]  # Get filename from path
        extracted_data = document_processor.extract_text_from_file(file_response, filename)

        # Validate that it's a resume
        is_valid, reason = document_processor.validate_resume_content(extracted_data)
        if not is_valid:
            raise Exception(f"Document validation failed: {reason}")

        # Parse resume using Claude
        logger.info(f"Parsing resume content with Claude for resume_id: {resume_id}")
        parsed_content = asyncio.run(claude_parser.parse_resume_content(
            extracted_data["text"],
            filename
        ))

        # Update resume with parsed content
        update_data = {
            "parsed_content": json.dumps(parsed_content),
            "parsing_status": "parsed",
            "parsed_at": datetime.now().isoformat(),
            "parsing_error": None
        }

        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully parsed resume {resume_id}")

        # Queue format scoring task
        score_format_task.delay(resume_id)

        return {
            "status": "success",
            "resume_id": resume_id,
            "parsed_sections": list(parsed_content.keys())
        }

    except Exception as e:
        logger.error(f"Resume parsing task failed for {resume_id}: {str(e)}")

        # Update status to failed
        supabase.table("resumes").update({
            "parsing_status": "failed",
            "parsing_error": str(e)
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True, max_retries=3)
def score_format_task(self, resume_id: str):
    """
    Score resume format and structure using Claude Sonnet 3.5

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"Starting format scoring for resume_id: {resume_id}")

        # Update scoring status
        supabase.table("resumes").update({
            "scoring_status": "scoring"
        }).eq("id", resume_id).execute()

        # Get resume data including parsed content
        resume_response = supabase.table("resumes").select(
            "parsed_content, file_path"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        resume_data = resume_response.data[0]
        parsed_content = json.loads(resume_data["parsed_content"])

        # Download original file to get text for format analysis
        file_path = resume_data["file_path"]
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise Exception(f"Failed to download file from Supabase: {file_path}")

        # Extract text from document
        filename = file_path.split('/')[-1]
        extracted_data = document_processor.extract_text_from_file(file_response, filename)
        original_text = extracted_data["text"]

        # Score format using Claude
        logger.info(f"Scoring format with Claude for resume_id: {resume_id}")
        claude_score, claude_feedback = asyncio.run(claude_parser.score_format_and_structure(
            parsed_content,
            original_text
        ))

        # Update resume with Claude scoring results
        update_data = {
            "claude_score": claude_score,
            "claude_feedback": json.dumps(claude_feedback),
            "scoring_status": "scored",
            "last_scored_at": datetime.now().isoformat(),
            "scoring_error": None
        }

        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully scored format for resume {resume_id}, score: {claude_score}")

        return {
            "status": "success",
            "resume_id": resume_id,
            "claude_score": claude_score,
            "feedback_items": len(claude_feedback.get("format_issues", []))
        }

    except Exception as e:
        logger.error(f"Format scoring task failed for {resume_id}: {str(e)}")

        # Update status to failed
        supabase.table("resumes").update({
            "scoring_status": "failed",
            "scoring_error": str(e)
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True)
def analyze_resume_task(self, resume_id: str):
    """
    Complete resume analysis workflow - parsing and scoring

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"Starting complete analysis for resume_id: {resume_id}")

        # Get resume record to get file path
        resume_response = supabase.table("resumes").select("file_path").eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        file_path = resume_response.data[0]["file_path"]
        if not file_path:
            raise Exception(f"No file path found for resume {resume_id}")

        # Start with parsing
        parse_result = parse_resume_task.delay(resume_id, file_path)

        return {
            "status": "started",
            "resume_id": resume_id,
            "parse_task_id": parse_result.id
        }

    except Exception as e:
        logger.error(f"Failed to start analysis for resume {resume_id}: {str(e)}")
        raise

@celery.task
def get_analysis_status(resume_id: str) -> Dict[str, Any]:
    """
    Get current analysis status for a resume

    Args:
        resume_id: UUID of the resume record

    Returns:
        Dict with current status information
    """
    try:
        resume_response = supabase.table("resumes").select(
            "parsing_status, scoring_status, parsed_at, last_scored_at, parsing_error, scoring_error"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            return {"error": f"Resume {resume_id} not found"}

        data = resume_response.data[0]

        return {
            "resume_id": resume_id,
            "parsing_status": data["parsing_status"],
            "scoring_status": data["scoring_status"],
            "parsed_at": data["parsed_at"],
            "last_scored_at": data["last_scored_at"],
            "parsing_error": data["parsing_error"],
            "scoring_error": data["scoring_error"],
            "overall_status": _determine_overall_status(data)
        }

    except Exception as e:
        logger.error(f"Failed to get analysis status for resume {resume_id}: {str(e)}")
        return {"error": str(e)}

def _determine_overall_status(data: Dict[str, Any]) -> str:
    """Determine overall analysis status based on parsing and scoring status"""
    parsing_status = data["parsing_status"]
    scoring_status = data["scoring_status"]

    if parsing_status == "failed":
        return "failed"
    elif parsing_status == "parsing":
        return "parsing"
    elif parsing_status == "parsed" and scoring_status == "pending":
        return "ready_for_scoring"
    elif scoring_status == "scoring":
        return "scoring"
    elif scoring_status == "scored":
        return "completed"
    elif scoring_status == "failed":
        return "scoring_failed"
    else:
        return "pending"
