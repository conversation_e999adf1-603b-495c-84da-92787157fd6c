"""
Resume Analysis Celery Tasks

Handles asynchronous processing of resume parsing and scoring
"""

import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime
from uuid import UUID

from celery import current_task
from ..celery_worker import celery
from ..services.supabase import supabase
from ..services.document_processor import document_processor
from ..services.claude_parser import claude_parser
from ..services.ai import AIServiceError

logger = logging.getLogger(__name__)

@celery.task(bind=True, max_retries=3)
def parse_resume_task(self, resume_id: str, file_path: str):
    """
    Parse resume content using Claude

    Args:
        resume_id: UUID of the resume record
        file_path: Path to the resume file in Supabase storage
    """
    try:
        logger.info(f"Starting resume parsing for resume_id: {resume_id}")

        # Update status to parsing
        supabase.table("resumes").update({
            "parsing_status": "parsing"
        }).eq("id", resume_id).execute()

        # Download file from Supabase storage
        try:
            file_response = supabase.storage.from_("resumes").download(file_path)
            if not file_response:
                raise Exception("Failed to download file from storage")

            file_content = file_response
            filename = file_path.split("/")[-1]  # Extract filename from path

        except Exception as e:
            logger.error(f"Failed to download file {file_path}: {str(e)}")
            raise Exception(f"File download failed: {str(e)}")

        # Extract text from document
        try:
            extracted_data = document_processor.extract_text_from_file(file_content, filename)
            extracted_text = extracted_data["text"]

            # Validate that it looks like a resume
            is_valid, reason = document_processor.validate_resume_content(extracted_data)
            if not is_valid:
                raise Exception(f"Document validation failed: {reason}")

        except Exception as e:
            logger.error(f"Document processing failed for resume_id {resume_id}: {str(e)}")
            # Update status to failed
            supabase.table("resumes").update({
                "parsing_status": "failed",
                "parsing_error": str(e)
            }).eq("id", resume_id).execute()
            raise

        # Parse content using Claude
        try:
            import asyncio
            parsed_content = asyncio.run(claude_parser.parse_resume_content(extracted_text, filename))

            # Update resume with parsed content
            update_data = {
                "parsed_content": json.dumps(parsed_content),
                "parsing_status": "parsed",
                "parsed_at": datetime.utcnow().isoformat(),
                "parsing_error": None
            }

            supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

            logger.info(f"Successfully parsed resume {resume_id}")

            # Queue format scoring task
            score_format_task.delay(resume_id, extracted_text)

            return {
                "status": "success",
                "resume_id": resume_id,
                "parsed_sections": list(parsed_content.keys())
            }

        except Exception as e:
            logger.error(f"Claude parsing failed for resume_id {resume_id}: {str(e)}")
            # Update status to failed
            supabase.table("resumes").update({
                "parsing_status": "failed",
                "parsing_error": str(e)
            }).eq("id", resume_id).execute()
            raise

    except Exception as e:
        logger.error(f"Resume parsing task failed for {resume_id}: {str(e)}")

        # Retry logic
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying parsing task for {resume_id}, attempt {self.request.retries + 1}")
            raise self.retry(countdown=60 * (2 ** self.request.retries))

        # Final failure - update database
        supabase.table("resumes").update({
            "parsing_status": "failed",
            "parsing_error": f"Parsing failed after {self.max_retries} retries: {str(e)}"
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True, max_retries=3)
def score_format_task(self, resume_id: str, original_text: str):
    """
    Score resume format and structure using Claude

    Args:
        resume_id: UUID of the resume record
        original_text: Original extracted text for format analysis
    """
    try:
        logger.info(f"Starting format scoring for resume_id: {resume_id}")

        # Get parsed content from database
        resume_response = supabase.table("resumes").select("parsed_content").eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        parsed_content_str = resume_response.data[0]["parsed_content"]
        if not parsed_content_str:
            raise Exception(f"No parsed content found for resume {resume_id}")

        parsed_content = json.loads(parsed_content_str)

        # Update scoring status
        supabase.table("resumes").update({
            "scoring_status": "scoring"
        }).eq("id", resume_id).execute()

        # Score format using Claude
        try:
            import asyncio
            claude_score, claude_feedback = asyncio.run(claude_parser.score_format_and_structure(
                parsed_content, original_text
            ))

            # Update resume with Claude scoring results
            update_data = {
                "claude_score": claude_score,
                "claude_feedback": json.dumps(claude_feedback),
                "scoring_error": None
            }

            # If this is the only scoring (no OpenAI), update overall score and status
            # For now, we'll leave scoring_status as 'scoring' until OpenAI is implemented
            # update_data["overall_score"] = claude_score * 0.25  # Claude is 25% of total
            # update_data["scoring_status"] = "scored"
            # update_data["last_scored_at"] = datetime.utcnow().isoformat()

            supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

            logger.info(f"Successfully scored format for resume {resume_id}, score: {claude_score}")

            return {
                "status": "success",
                "resume_id": resume_id,
                "claude_score": claude_score,
                "feedback_items": len(claude_feedback.get("format_issues", []))
            }

        except Exception as e:
            logger.error(f"Claude format scoring failed for resume_id {resume_id}: {str(e)}")
            # Update status to failed
            supabase.table("resumes").update({
                "scoring_status": "failed",
                "scoring_error": str(e)
            }).eq("id", resume_id).execute()
            raise

    except Exception as e:
        logger.error(f"Format scoring task failed for {resume_id}: {str(e)}")

        # Retry logic
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying format scoring task for {resume_id}, attempt {self.request.retries + 1}")
            raise self.retry(countdown=60 * (2 ** self.request.retries))

        # Final failure - update database
        supabase.table("resumes").update({
            "scoring_status": "failed",
            "scoring_error": f"Format scoring failed after {self.max_retries} retries: {str(e)}"
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True)
def analyze_resume_task(self, resume_id: str):
    """
    Complete resume analysis workflow - parsing and scoring

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"Starting complete analysis for resume_id: {resume_id}")

        # Get resume record to get file path
        resume_response = supabase.table("resumes").select("file_path").eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        file_path = resume_response.data[0]["file_path"]
        if not file_path:
            raise Exception(f"No file path found for resume {resume_id}")

        # Start with parsing
        parse_result = parse_resume_task.delay(resume_id, file_path)

        return {
            "status": "started",
            "resume_id": resume_id,
            "parse_task_id": parse_result.id
        }

    except Exception as e:
        logger.error(f"Failed to start analysis for resume {resume_id}: {str(e)}")
        raise

@celery.task
def get_analysis_status(resume_id: str) -> Dict[str, Any]:
    """
    Get current analysis status for a resume

    Args:
        resume_id: UUID of the resume record

    Returns:
        Dict with current status information
    """
    try:
        resume_response = supabase.table("resumes").select(
            "parsing_status, scoring_status, parsed_at, last_scored_at, parsing_error, scoring_error"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            return {"error": f"Resume {resume_id} not found"}

        data = resume_response.data[0]

        return {
            "resume_id": resume_id,
            "parsing_status": data["parsing_status"],
            "scoring_status": data["scoring_status"],
            "parsed_at": data["parsed_at"],
            "last_scored_at": data["last_scored_at"],
            "parsing_error": data["parsing_error"],
            "scoring_error": data["scoring_error"],
            "overall_status": _determine_overall_status(data)
        }

    except Exception as e:
        logger.error(f"Failed to get analysis status for resume {resume_id}: {str(e)}")
        return {"error": str(e)}

def _determine_overall_status(data: Dict[str, Any]) -> str:
    """Determine overall analysis status based on parsing and scoring status"""
    parsing_status = data["parsing_status"]
    scoring_status = data["scoring_status"]

    if parsing_status == "failed":
        return "failed"
    elif parsing_status == "parsing":
        return "parsing"
    elif parsing_status == "parsed" and scoring_status == "pending":
        return "ready_for_scoring"
    elif scoring_status == "scoring":
        return "scoring"
    elif scoring_status == "scored":
        return "completed"
    elif scoring_status == "failed":
        return "scoring_failed"
    else:
        return "pending"
