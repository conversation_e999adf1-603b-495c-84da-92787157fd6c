-- Migration: Add parsing and scoring status fields to resumes table
-- Date: 2024-01-XX
-- Description: Adds status tracking fields for the Claude analysis workflow

-- Add parsing and scoring status columns to resumes table
ALTER TABLE resumes 
ADD COLUMN IF NOT EXISTS parsing_status TEXT DEFAULT 'pending' CHECK (parsing_status IN ('pending', 'parsing', 'parsed', 'failed')),
ADD COLUMN IF NOT EXISTS scoring_status TEXT DEFAULT 'pending' CHECK (scoring_status IN ('pending', 'scoring', 'scored', 'failed')),
ADD COLUMN IF NOT EXISTS parsed_content JSONB,
ADD COLUMN IF NOT EXISTS parsed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS parsing_error TEXT,
ADD COLUMN IF NOT EXISTS scoring_error TEXT;

-- Add indexes for better performance on status queries
CREATE INDEX IF NOT EXISTS idx_resumes_parsing_status ON resumes(parsing_status);
CREATE INDEX IF NOT EXISTS idx_resumes_scoring_status ON resumes(scoring_status);
CREATE INDEX IF NOT EXISTS idx_resumes_parsed_at ON resumes(parsed_at);

-- Update scoring_history table to include parsing fields
ALTER TABLE scoring_history 
ADD COLUMN IF NOT EXISTS parsed_content JSONB,
ADD COLUMN IF NOT EXISTS parsing_version TEXT DEFAULT '1.0';

-- Add comments for documentation
COMMENT ON COLUMN resumes.parsing_status IS 'Status of resume parsing: pending, parsing, parsed, failed';
COMMENT ON COLUMN resumes.scoring_status IS 'Status of resume scoring: pending, scoring, scored, failed';
COMMENT ON COLUMN resumes.parsed_content IS 'Structured JSON content extracted from resume by Claude';
COMMENT ON COLUMN resumes.parsed_at IS 'Timestamp when resume was successfully parsed';
COMMENT ON COLUMN resumes.parsing_error IS 'Error message if parsing failed';
COMMENT ON COLUMN resumes.scoring_error IS 'Error message if scoring failed';

-- Verify the new columns were added
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'resumes' 
AND column_name IN ('parsing_status', 'scoring_status', 'parsed_content', 'parsed_at', 'parsing_error', 'scoring_error')
ORDER BY column_name;
