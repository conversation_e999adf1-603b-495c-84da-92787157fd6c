"""
Document Processing Service

Handles extraction of text content from PDF and DOCX files for resume analysis.
"""

import logging
import tempfile
import os
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import PyPDF2
import pdfplumber
from docx import Document
import io

logger = logging.getLogger(__name__)

class DocumentProcessingError(Exception):
    """Base exception for document processing errors"""
    pass

class UnsupportedFileFormatError(DocumentProcessingError):
    """Raised when file format is not supported"""
    pass

class DocumentCorruptedError(DocumentProcessingError):
    """Raised when document is corrupted or unreadable"""
    pass

class DocumentProcessor:
    """Service for processing PDF and DOCX documents"""
    
    SUPPORTED_FORMATS = {'.pdf', '.docx'}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_text_from_file(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Extract text content from PDF or DOCX file
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename to determine format
            
        Returns:
            Dict containing extracted text and metadata
            
        Raises:
            DocumentProcessingError: If extraction fails
        """
        try:
            # Validate file size
            if len(file_content) > self.MAX_FILE_SIZE:
                raise DocumentProcessingError(f"File size exceeds maximum limit of {self.MAX_FILE_SIZE} bytes")
            
            # Determine file format
            file_extension = Path(filename).suffix.lower()
            if file_extension not in self.SUPPORTED_FORMATS:
                raise UnsupportedFileFormatError(f"Unsupported file format: {file_extension}")
            
            # Extract text based on format
            if file_extension == '.pdf':
                return self._extract_from_pdf(file_content, filename)
            elif file_extension == '.docx':
                return self._extract_from_docx(file_content, filename)
            
        except Exception as e:
            self.logger.error(f"Document processing failed for {filename}: {str(e)}")
            raise DocumentProcessingError(f"Failed to process document: {str(e)}")
    
    def _extract_from_pdf(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Extract text from PDF file using multiple methods for reliability"""
        try:
            # Method 1: Try pdfplumber first (better for complex layouts)
            try:
                with io.BytesIO(file_content) as pdf_stream:
                    with pdfplumber.open(pdf_stream) as pdf:
                        text_content = ""
                        page_count = len(pdf.pages)
                        
                        for page_num, page in enumerate(pdf.pages):
                            page_text = page.extract_text()
                            if page_text:
                                text_content += f"\n--- Page {page_num + 1} ---\n"
                                text_content += page_text
                        
                        if text_content.strip():
                            return {
                                "text": text_content.strip(),
                                "page_count": page_count,
                                "extraction_method": "pdfplumber",
                                "file_format": "pdf",
                                "filename": filename
                            }
            except Exception as e:
                self.logger.warning(f"pdfplumber extraction failed for {filename}: {str(e)}")
            
            # Method 2: Fallback to PyPDF2
            try:
                with io.BytesIO(file_content) as pdf_stream:
                    pdf_reader = PyPDF2.PdfReader(pdf_stream)
                    text_content = ""
                    page_count = len(pdf_reader.pages)
                    
                    for page_num, page in enumerate(pdf_reader.pages):
                        page_text = page.extract_text()
                        if page_text:
                            text_content += f"\n--- Page {page_num + 1} ---\n"
                            text_content += page_text
                    
                    if text_content.strip():
                        return {
                            "text": text_content.strip(),
                            "page_count": page_count,
                            "extraction_method": "PyPDF2",
                            "file_format": "pdf",
                            "filename": filename
                        }
            except Exception as e:
                self.logger.warning(f"PyPDF2 extraction failed for {filename}: {str(e)}")
            
            raise DocumentCorruptedError("Unable to extract text from PDF using any method")
            
        except Exception as e:
            if isinstance(e, DocumentProcessingError):
                raise
            raise DocumentCorruptedError(f"PDF processing failed: {str(e)}")
    
    def _extract_from_docx(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Extract text from DOCX file"""
        try:
            with io.BytesIO(file_content) as docx_stream:
                doc = Document(docx_stream)
                
                # Extract text from paragraphs
                text_content = ""
                paragraph_count = 0
                
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        text_content += paragraph.text + "\n"
                        paragraph_count += 1
                
                # Extract text from tables
                table_count = 0
                for table in doc.tables:
                    table_count += 1
                    text_content += f"\n--- Table {table_count} ---\n"
                    for row in table.rows:
                        row_text = []
                        for cell in row.cells:
                            if cell.text.strip():
                                row_text.append(cell.text.strip())
                        if row_text:
                            text_content += " | ".join(row_text) + "\n"
                
                if not text_content.strip():
                    raise DocumentCorruptedError("No readable text found in DOCX file")
                
                return {
                    "text": text_content.strip(),
                    "paragraph_count": paragraph_count,
                    "table_count": table_count,
                    "extraction_method": "python-docx",
                    "file_format": "docx",
                    "filename": filename
                }
                
        except Exception as e:
            if isinstance(e, DocumentProcessingError):
                raise
            raise DocumentCorruptedError(f"DOCX processing failed: {str(e)}")
    
    def validate_resume_content(self, extracted_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Basic validation to check if extracted content looks like a resume
        
        Args:
            extracted_data: Result from extract_text_from_file
            
        Returns:
            Tuple of (is_valid, reason)
        """
        text = extracted_data.get("text", "").lower()
        
        # Check minimum length
        if len(text) < 100:
            return False, "Document too short to be a resume"
        
        # Check for common resume keywords
        resume_keywords = [
            "experience", "education", "skills", "work", "employment",
            "university", "college", "degree", "certification", "project",
            "email", "phone", "address", "linkedin", "github"
        ]
        
        keyword_count = sum(1 for keyword in resume_keywords if keyword in text)
        
        if keyword_count < 3:
            return False, "Document doesn't appear to contain resume content"
        
        # Check for suspicious content that indicates non-resume documents
        non_resume_indicators = [
            "terms of service", "privacy policy", "user agreement",
            "invoice", "receipt", "purchase order", "contract",
            "meeting minutes", "agenda", "presentation slides"
        ]
        
        for indicator in non_resume_indicators:
            if indicator in text:
                return False, f"Document appears to be a {indicator.replace('_', ' ')}"
        
        return True, "Document appears to be a valid resume"
    
    def get_file_metadata(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Extract basic file metadata
        
        Args:
            file_content: Raw file content
            filename: Original filename
            
        Returns:
            Dict with file metadata
        """
        return {
            "filename": filename,
            "file_size": len(file_content),
            "file_extension": Path(filename).suffix.lower(),
            "is_supported_format": Path(filename).suffix.lower() in self.SUPPORTED_FORMATS
        }

# Global document processor instance
document_processor = DocumentProcessor()

async def get_document_processor() -> DocumentProcessor:
    """Dependency injection for document processor"""
    return document_processor
