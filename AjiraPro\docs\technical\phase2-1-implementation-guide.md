# Phase 2.1 Implementation Guide: Claude Integration

## Overview
Phase 2.1 implements Claude Sonnet 3.5 integration for resume parsing and format scoring. This phase handles the first stage of the resume analysis workflow.

## Completed Implementation

### 1. Document Processing Service ✅
**File**: `app/services/document_processor.py`

**Features**:
- **Multi-format Support**: PDF and DOCX file processing
- **Dual PDF Extraction**: pdfplumber (primary) + PyPDF2 (fallback)
- **Content Validation**: Basic resume content detection
- **Error Handling**: Comprehensive error types and recovery
- **File Size Limits**: 10MB maximum file size

**Key Methods**:
- `extract_text_from_file()` - Main extraction method
- `validate_resume_content()` - Resume content validation
- `get_file_metadata()` - File information extraction

### 2. Claude Parser Service ✅
**File**: `app/services/claude_parser.py`

**Features**:
- **Resume Parsing**: Extract structured JSON from resume text
- **Format Scoring**: ATS compatibility analysis based on scoring guidelines
- **<PERSON> 3.5**: Uses latest Claude model for best results
- **Structured Feedback**: Detailed format recommendations

**Key Methods**:
- `parse_resume_content()` - Parse resume into structured JSON
- `score_format_and_structure()` - Score format and provide feedback

### 3. Celery Task Integration ✅
**File**: `app/tasks/resume_analysis.py`

**Features**:
- **Asynchronous Processing**: Non-blocking resume analysis
- **Task Chaining**: Automatic progression from parsing to scoring
- **Retry Logic**: Automatic retries with exponential backoff
- **Status Tracking**: Real-time progress updates
- **Error Recovery**: Graceful failure handling

**Key Tasks**:
- `parse_resume_task()` - Handle Claude parsing
- `score_format_task()` - Handle Claude format scoring
- `analyze_resume_task()` - Complete workflow orchestration

### 4. API Endpoints ✅
**File**: `app/routers/resume_analysis.py`

**Features**:
- **Analysis Triggers**: Start complete analysis workflow
- **Status Monitoring**: Real-time progress tracking
- **Result Retrieval**: Get parsed content and feedback
- **Re-processing**: Force re-analysis of resumes

**Key Endpoints**:
- `POST /api/resumes/{id}/analyze` - Start analysis
- `GET /api/resumes/{id}/analysis-status` - Check progress
- `GET /api/resumes/{id}/parsed-content` - Get structured data
- `GET /api/resumes/{id}/claude-feedback` - Get format feedback

## Workflow Implementation

### Complete Analysis Flow
```
1. User triggers analysis → POST /api/resumes/{id}/analyze
2. analyze_resume_task starts → Queues parse_resume_task
3. parse_resume_task executes:
   - Downloads file from Supabase storage
   - Extracts text using document_processor
   - Validates content as resume
   - Calls Claude for parsing
   - Saves structured JSON to parsed_content
   - Queues score_format_task
4. score_format_task executes:
   - Uses parsed content + original text
   - Calls Claude for format scoring
   - Saves claude_score and claude_feedback
5. User gets results → Various GET endpoints
```

### Database Updates
```sql
-- During parsing
UPDATE resumes SET 
  parsing_status = 'parsing' → 'parsed',
  parsed_content = '{"personal_info": {...}, ...}',
  parsed_at = NOW()

-- During format scoring  
UPDATE resumes SET
  scoring_status = 'scoring',
  claude_score = 85.5,
  claude_feedback = '{"format_issues": [...], ...}'
```

## Claude Integration Details

### Parsing Prompt Structure
The parsing prompt instructs Claude to:
- Extract personal information (name, email, phone, etc.)
- Structure experience with achievements and technologies
- Organize education with degrees and institutions
- Categorize skills (technical, soft, tools, languages)
- Identify certifications, projects, and additional sections

### Format Scoring Criteria
Based on the scoring guidelines, Claude evaluates:

**High Score (90-100%)**:
- Reverse chronological format
- Standard section headings
- ATS-friendly fonts (Arial, Calibri, Times New Roman)
- No graphics, tables, or complex formatting
- Consistent date formatting (MM/YYYY)
- Proper bullet point usage (max 2 lines)
- Contact info in text (not headers/footers)

**Low Score (10-30%)**:
- Creative/functional formats
- Graphics, charts, tables present
- Multiple columns
- Non-standard fonts or excessive formatting
- Poor bullet point usage
- Critical info in headers/footers

### Feedback Structure
```json
{
  "format_issues": [
    {
      "issue": "Non-standard date format",
      "severity": "medium",
      "recommendation": "Use MM/YYYY format consistently",
      "section": "experience"
    }
  ],
  "ats_compatibility": {
    "score": 75,
    "analysis": {...}
  }
}
```

## Dependencies Added

### New Requirements
```
PyPDF2==3.0.1          # PDF text extraction (fallback)
pdfplumber==0.10.3      # PDF text extraction (primary)
python-docx==1.1.0      # DOCX text extraction
```

### Updated Configuration
- Claude Sonnet 3.5 model: `claude-3-5-sonnet-20241022`
- Timeout settings: 60 seconds default
- Retry logic: 3 attempts with exponential backoff

## Testing the Implementation

### 1. Upload a Resume
```bash
# Upload resume file first using existing upload endpoint
POST /api/resumes/upload
```

### 2. Start Analysis
```bash
curl -X POST "http://localhost:8000/api/resumes/{resume_id}/analyze" \
  -H "Authorization: Bearer {token}"
```

### 3. Monitor Progress
```bash
curl "http://localhost:8000/api/resumes/{resume_id}/analysis-status" \
  -H "Authorization: Bearer {token}"
```

### 4. Get Results
```bash
# Get parsed content
curl "http://localhost:8000/api/resumes/{resume_id}/parsed-content" \
  -H "Authorization: Bearer {token}"

# Get Claude feedback
curl "http://localhost:8000/api/resumes/{resume_id}/claude-feedback" \
  -H "Authorization: Bearer {token}"
```

## Expected Results

### Parsing Success
- `parsing_status`: "parsed"
- `parsed_content`: Structured JSON with all resume sections
- `parsed_at`: Timestamp of completion

### Format Scoring Success
- `claude_score`: Numeric score (0-100)
- `claude_feedback`: Detailed format recommendations
- Specific issues identified with severity levels

## Error Handling

### Common Issues
1. **File Download Failures**: Network issues, missing files
2. **Text Extraction Failures**: Corrupted files, unsupported formats
3. **Claude API Failures**: Rate limits, service unavailability
4. **Parsing Failures**: Invalid JSON responses, timeout issues

### Recovery Mechanisms
- **Automatic Retries**: 3 attempts with exponential backoff
- **Fallback Methods**: Multiple PDF extraction libraries
- **Status Tracking**: Clear error messages in database
- **Graceful Degradation**: Partial results when possible

## Performance Metrics

### Target Performance
- **Parsing Time**: <30 seconds for typical resumes
- **Success Rate**: >95% for standard resume formats
- **Error Recovery**: <5% permanent failures
- **API Response**: <2 seconds for status checks

### Monitoring Points
- Task execution times
- Claude API response times
- File processing success rates
- Error frequency by type

## Next Steps (Phase 2.2)

1. **OpenAI Content Scoring**: Implement content analysis
2. **Score Calculation**: Combine Claude + OpenAI scores
3. **Frontend Integration**: Display results in UI
4. **Performance Optimization**: Caching and parallel processing

## Environment Variables Required

```env
# Already configured in Phase 1
CLAUDE_API_KEY=your_claude_api_key
OPENAI_API_KEY=your_openai_api_key
CLAUDE_MODEL=claude-3-5-sonnet-20241022
AI_REQUEST_TIMEOUT=60
AI_MAX_RETRIES=3
```

Phase 2.1 is now complete and ready for testing. The system can successfully parse resumes and provide format scoring using Claude Sonnet 3.5.
