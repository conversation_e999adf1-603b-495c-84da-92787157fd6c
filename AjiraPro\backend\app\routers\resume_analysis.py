"""
Resume Analysis API Endpoints

Handles resume parsing and scoring operations
"""

import logging
from typing import Dict, Any, Optional
from uuid import UUID
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import J<PERSON>NResponse

from ..core.security import get_current_user
from ..services.supabase import supabase
from ..tasks.resume_analysis import analyze_resume_task, get_analysis_status
from ..models.resume import ParsingRequest, ParsingResponse, ScoringRequest

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/resumes", tags=["resume-analysis"])

@router.post("/{resume_id}/analyze")
async def analyze_resume(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Start complete resume analysis (parsing + scoring)
    
    Args:
        resume_id: UUID of the resume to analyze
        current_user: Current authenticated user
        
    Returns:
        Analysis task information
    """
    try:
        # Verify resume belongs to user
        resume_response = supabase.table("resumes").select(
            "id, user_id, file_path, parsing_status, scoring_status"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()
        
        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )
        
        resume_data = resume_response.data[0]
        
        # Check if file exists
        if not resume_data["file_path"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file associated with this resume"
            )
        
        # Check if analysis is already in progress
        if resume_data["parsing_status"] == "parsing":
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "message": "Analysis already in progress",
                    "status": "parsing",
                    "resume_id": str(resume_id)
                }
            )
        
        if resume_data["scoring_status"] == "scoring":
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "message": "Scoring already in progress", 
                    "status": "scoring",
                    "resume_id": str(resume_id)
                }
            )
        
        # Start analysis
        task_result = analyze_resume_task.delay(str(resume_id))
        
        return JSONResponse(
            status_code=status.HTTP_202_ACCEPTED,
            content={
                "message": "Resume analysis started",
                "resume_id": str(resume_id),
                "task_id": task_result.id,
                "status": "started"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start analysis for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start analysis: {str(e)}"
        )

@router.get("/{resume_id}/analysis-status")
async def get_resume_analysis_status(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Get current analysis status for a resume
    
    Args:
        resume_id: UUID of the resume
        current_user: Current authenticated user
        
    Returns:
        Current analysis status
    """
    try:
        # Verify resume belongs to user
        resume_response = supabase.table("resumes").select("id, user_id").eq(
            "id", str(resume_id)
        ).eq("user_id", current_user["id"]).execute()
        
        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )
        
        # Get detailed status
        status_result = get_analysis_status.delay(str(resume_id))
        status_data = status_result.get(timeout=10)
        
        if "error" in status_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=status_data["error"]
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=status_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get analysis status for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analysis status: {str(e)}"
        )

@router.get("/{resume_id}/parsed-content")
async def get_parsed_content(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Get parsed resume content
    
    Args:
        resume_id: UUID of the resume
        current_user: Current authenticated user
        
    Returns:
        Parsed resume content in JSON format
    """
    try:
        # Verify resume belongs to user and get parsed content
        resume_response = supabase.table("resumes").select(
            "id, user_id, parsed_content, parsing_status, parsed_at"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()
        
        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )
        
        resume_data = resume_response.data[0]
        
        if resume_data["parsing_status"] != "parsed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Resume not yet parsed. Current status: {resume_data['parsing_status']}"
            )
        
        if not resume_data["parsed_content"]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No parsed content available"
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "resume_id": str(resume_id),
                "parsed_content": resume_data["parsed_content"],
                "parsed_at": resume_data["parsed_at"],
                "parsing_status": resume_data["parsing_status"]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get parsed content for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get parsed content: {str(e)}"
        )

@router.get("/{resume_id}/claude-feedback")
async def get_claude_feedback(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Get Claude format scoring feedback
    
    Args:
        resume_id: UUID of the resume
        current_user: Current authenticated user
        
    Returns:
        Claude feedback and scoring data
    """
    try:
        # Verify resume belongs to user and get Claude feedback
        resume_response = supabase.table("resumes").select(
            "id, user_id, claude_score, claude_feedback, scoring_status"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()
        
        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )
        
        resume_data = resume_response.data[0]
        
        if resume_data["claude_score"] is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Claude scoring not completed. Current status: {resume_data['scoring_status']}"
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "resume_id": str(resume_id),
                "claude_score": resume_data["claude_score"],
                "claude_feedback": resume_data["claude_feedback"],
                "scoring_status": resume_data["scoring_status"]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get Claude feedback for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Claude feedback: {str(e)}"
        )

@router.post("/{resume_id}/reparse")
async def reparse_resume(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Force re-parsing of a resume
    
    Args:
        resume_id: UUID of the resume to reparse
        current_user: Current authenticated user
        
    Returns:
        Re-parsing task information
    """
    try:
        # Verify resume belongs to user
        resume_response = supabase.table("resumes").select(
            "id, user_id, file_path"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()
        
        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )
        
        resume_data = resume_response.data[0]
        
        if not resume_data["file_path"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file associated with this resume"
            )
        
        # Reset parsing and scoring status
        supabase.table("resumes").update({
            "parsing_status": "pending",
            "scoring_status": "pending",
            "parsed_content": None,
            "claude_score": None,
            "claude_feedback": None,
            "parsing_error": None,
            "scoring_error": None
        }).eq("id", str(resume_id)).execute()
        
        # Start analysis
        task_result = analyze_resume_task.delay(str(resume_id))
        
        return JSONResponse(
            status_code=status.HTTP_202_ACCEPTED,
            content={
                "message": "Resume re-parsing started",
                "resume_id": str(resume_id),
                "task_id": task_result.id,
                "status": "started"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start re-parsing for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start re-parsing: {str(e)}"
        )
