"""
Resume Analysis API Endpoints

Handles resume parsing and scoring operations
"""

import logging
from typing import Dict, Any
from uuid import UUID
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import JSONResponse

from ..core.security import get_current_user
from ..services.supabase import supabase
from ..tasks.resume_analysis import analyze_resume_task
from ..services.resume_validator import ai_resume_validator

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/resumes", tags=["resume-analysis"])

@router.post("/{resume_id}/analyze")
async def analyze_resume(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Start complete resume analysis (parsing + scoring)

    Args:
        resume_id: UUID of the resume to analyze
        current_user: Current authenticated user

    Returns:
        Analysis task information
    """
    try:
        # Verify resume belongs to user
        resume_response = supabase.table("resumes").select(
            "id, user_id, file_path, parsing_status, scoring_status"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        resume_data = resume_response.data[0]

        # Check if file exists
        if not resume_data["file_path"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file associated with this resume"
            )

        # Check if analysis is already in progress
        if resume_data["parsing_status"] == "parsing":
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "message": "Analysis already in progress",
                    "status": "parsing",
                    "resume_id": str(resume_id)
                }
            )

        if resume_data["scoring_status"] == "scoring":
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "message": "Scoring already in progress",
                    "status": "scoring",
                    "resume_id": str(resume_id)
                }
            )

        # Start analysis using Celery task
        try:
            # Queue the analysis task
            task_result = analyze_resume_task.delay(str(resume_id))

            return JSONResponse(
                status_code=status.HTTP_202_ACCEPTED,
                content={
                    "message": "Resume analysis started successfully",
                    "resume_id": str(resume_id),
                    "task_id": task_result.id,
                    "status": "processing"
                }
            )

        except Exception as analysis_error:
            logger.error(f"Failed to start analysis for resume {resume_id}: {str(analysis_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to start analysis: {str(analysis_error)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start analysis for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start analysis: {str(e)}"
        )

@router.get("/{resume_id}/analysis-status")
async def get_resume_analysis_status(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Get current analysis status for a resume

    Args:
        resume_id: UUID of the resume
        current_user: Current authenticated user

    Returns:
        Current analysis status
    """
    try:
        # Verify resume belongs to user
        resume_response = supabase.table("resumes").select("id, user_id").eq(
            "id", str(resume_id)
        ).eq("user_id", current_user["id"]).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        # Get detailed status directly from database
        resume_response = supabase.table("resumes").select(
            "parsing_status, scoring_status, parsed_at, last_scored_at, parsing_error, scoring_error"
        ).eq("id", str(resume_id)).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        data = resume_response.data[0]

        # Determine overall status
        parsing_status = data["parsing_status"]
        scoring_status = data["scoring_status"]

        if parsing_status == "failed":
            overall_status = "failed"
        elif parsing_status == "parsing":
            overall_status = "parsing"
        elif parsing_status == "parsed" and scoring_status == "pending":
            overall_status = "ready_for_scoring"
        elif scoring_status == "scoring":
            overall_status = "scoring"
        elif scoring_status == "scored":
            overall_status = "completed"
        elif scoring_status == "failed":
            overall_status = "scoring_failed"
        else:
            overall_status = "pending"

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "resume_id": str(resume_id),
                "parsing_status": data["parsing_status"],
                "scoring_status": data["scoring_status"],
                "parsed_at": data["parsed_at"],
                "last_scored_at": data["last_scored_at"],
                "parsing_error": data["parsing_error"],
                "scoring_error": data["scoring_error"],
                "overall_status": overall_status
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get analysis status for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analysis status: {str(e)}"
        )

@router.get("/{resume_id}/claude-feedback")
async def get_claude_feedback(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Get Claude format scoring feedback

    Args:
        resume_id: UUID of the resume
        current_user: Current authenticated user

    Returns:
        Claude feedback and scoring data
    """
    try:
        # Verify resume belongs to user and get Claude feedback
        resume_response = supabase.table("resumes").select(
            "id, user_id, claude_score, claude_feedback, scoring_status"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        resume_data = resume_response.data[0]

        if resume_data["claude_score"] is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Claude scoring not completed. Current status: {resume_data['scoring_status']}"
            )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "resume_id": str(resume_id),
                "claude_score": resume_data["claude_score"],
                "claude_feedback": resume_data["claude_feedback"],
                "scoring_status": resume_data["scoring_status"]
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get Claude feedback for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Claude feedback: {str(e)}"
        )

@router.post("/{resume_id}/validate")
async def validate_resume_ai(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Fast AI-based validation to check if document is a resume

    Args:
        resume_id: UUID of the resume
        current_user: Current authenticated user

    Returns:
        AI validation results
    """
    try:
        # Verify resume belongs to user
        resume_response = supabase.table("resumes").select(
            "id, user_id, file_path, title"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        resume_data = resume_response.data[0]
        file_path = resume_data["file_path"]

        if not file_path:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file associated with this resume"
            )

        # Download file from Supabase storage
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume file not found in storage"
            )

        # Validate using AI
        filename = file_path.split('/')[-1]
        validation_result = await ai_resume_validator.validate_document_as_resume(
            file_response, filename
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "resume_id": str(resume_id),
                "filename": resume_data["title"],
                "validation": validation_result
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to validate resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Validation failed: {str(e)}"
        )
