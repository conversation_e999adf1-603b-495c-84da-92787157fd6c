"""
AI-Based Resume Validator Service

Uses OpenAI GPT-3.5 to quickly determine if a document is a resume
without needing to extract full text content.
"""

import json
import logging
from typing import Dict, Any, Tuple
import io

from .ai import ai_service, AIServiceError

logger = logging.getLogger(__name__)

class ResumeValidationError(Exception):
    """Exception raised when resume validation fails"""
    pass

class AIResumeValidator:
    """Service for validating resumes using OpenAI"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model = "gpt-3.5-turbo"  # Fast and cost-effective for validation
    
    async def validate_document_as_resume(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Use OpenAI to quickly determine if a document is a resume
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename
            
        Returns:
            Dict with validation results
            
        Raises:
            ResumeValidationError: If validation fails
        """
        try:
            # Extract a sample of text for validation (first 2000 characters)
            sample_text = self._extract_sample_text(file_content, filename)
            
            if not sample_text or len(sample_text.strip()) < 50:
                return {
                    "is_resume": False,
                    "likelihood": 0,
                    "reason": "Document appears to be empty or too short",
                    "confidence": "high"
                }
            
            # Create validation prompt
            validation_prompt = self._create_validation_prompt(sample_text, filename)
            
            messages = [
                {
                    "role": "system",
                    "content": "You are an expert document classifier. Analyze documents to determine if they are resumes/CVs. Always respond with valid JSON only."
                },
                {
                    "role": "user",
                    "content": validation_prompt
                }
            ]
            
            # Call OpenAI for validation
            response = await ai_service.call_openai(messages, model=self.model)
            
            # Extract content from OpenAI response
            if "choices" in response and len(response["choices"]) > 0:
                validation_result = response["choices"][0]["message"]["content"]
            else:
                raise ResumeValidationError("Invalid response format from OpenAI")
            
            # Parse JSON response
            try:
                result_data = json.loads(validation_result)
                return self._structure_validation_result(result_data)
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse OpenAI validation response: {str(e)}")
                raise ResumeValidationError(f"Invalid JSON response from OpenAI: {str(e)}")
                
        except AIServiceError as e:
            self.logger.error(f"OpenAI API error during validation: {str(e)}")
            raise ResumeValidationError(f"OpenAI API error: {str(e)}")
        except Exception as e:
            self.logger.error(f"Unexpected error during validation: {str(e)}")
            raise ResumeValidationError(f"Validation failed: {str(e)}")
    
    def _extract_sample_text(self, file_content: bytes, filename: str) -> str:
        """Extract a sample of text from the document for validation"""
        try:
            file_extension = filename.lower().split('.')[-1]
            
            if file_extension == 'pdf':
                return self._extract_pdf_sample(file_content)
            elif file_extension == 'docx':
                return self._extract_docx_sample(file_content)
            else:
                # For other formats, try to decode as text
                try:
                    return file_content.decode('utf-8')[:2000]
                except UnicodeDecodeError:
                    return ""
        except Exception as e:
            self.logger.warning(f"Failed to extract sample text: {str(e)}")
            return ""
    
    def _extract_pdf_sample(self, file_content: bytes) -> str:
        """Extract sample text from PDF"""
        try:
            import PyPDF2
            with io.BytesIO(file_content) as pdf_stream:
                pdf_reader = PyPDF2.PdfReader(pdf_stream)
                text = ""
                # Extract from first 2 pages only for speed
                for page_num in range(min(2, len(pdf_reader.pages))):
                    page_text = pdf_reader.pages[page_num].extract_text()
                    text += page_text
                    if len(text) > 2000:
                        break
                return text[:2000]
        except Exception as e:
            self.logger.warning(f"PDF sample extraction failed: {str(e)}")
            return ""
    
    def _extract_docx_sample(self, file_content: bytes) -> str:
        """Extract sample text from DOCX"""
        try:
            from docx import Document
            with io.BytesIO(file_content) as docx_stream:
                doc = Document(docx_stream)
                text = ""
                # Extract from first few paragraphs only for speed
                for paragraph in doc.paragraphs[:20]:
                    text += paragraph.text + "\n"
                    if len(text) > 2000:
                        break
                return text[:2000]
        except Exception as e:
            self.logger.warning(f"DOCX sample extraction failed: {str(e)}")
            return ""
    
    def _create_validation_prompt(self, sample_text: str, filename: str) -> str:
        """Create prompt for resume validation"""
        return f"""
Analyze the following document sample to determine if it's a resume/CV.

FILENAME: {filename}

DOCUMENT SAMPLE:
{sample_text}

Determine if this document is a resume/CV based on:
1. Presence of personal information (name, contact details)
2. Work experience or employment history
3. Education background
4. Skills or qualifications
5. Professional summary or objective
6. Overall structure and format typical of resumes

Respond with this exact JSON format:
{{
  "is_resume": true/false,
  "likelihood": 85,
  "reason": "Brief explanation of why this is or isn't a resume",
  "confidence": "high/medium/low"
}}

IMPORTANT:
- likelihood should be 0-100 (percentage)
- is_resume should be true if likelihood >= 70
- confidence should reflect how certain you are
- reason should be concise (max 100 characters)
- Return only valid JSON, no additional text
"""
    
    def _structure_validation_result(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Structure validation result into standardized format"""
        is_resume = data.get("is_resume", False)
        likelihood = int(data.get("likelihood", 0))
        reason = data.get("reason", "Unknown")
        confidence = data.get("confidence", "low")
        
        # Ensure consistency
        if likelihood >= 70 and not is_resume:
            is_resume = True
        elif likelihood < 70 and is_resume:
            is_resume = False
        
        return {
            "is_resume": is_resume,
            "likelihood": likelihood,
            "reason": reason,
            "confidence": confidence,
            "validation_method": "openai_gpt35"
        }

# Global AI resume validator instance
ai_resume_validator = AIResumeValidator()

async def get_ai_resume_validator() -> AIResumeValidator:
    """Dependency injection for AI resume validator"""
    return ai_resume_validator
