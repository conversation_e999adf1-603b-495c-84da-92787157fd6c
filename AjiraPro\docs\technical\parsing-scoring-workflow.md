# Resume Parsing + Scoring Workflow

## Overview
The FajiraPro resume analysis system follows a two-stage process:

1. **Parsing Stage (<PERSON>)**: Extract structured content from PDF/DOCX files
2. **Scoring Stage (OpenAI + Claude)**: Analyze the parsed content and generate scores

This approach ensures consistent, high-quality analysis by separating content extraction from evaluation.

## Workflow Architecture

```
PDF/DOCX Upload → Claude Parsing → Structured JSON → OpenAI Analysis → Scores + Feedback
                      ↓                ↓                    ↓
                 parsed_content    scoring_input      final_scores
```

## Stage 1: Resume Parsing (<PERSON>)

### Purpose
- Extract structured content from resume files (PDF/DOCX)
- Standardize format for consistent analysis
- Handle various resume layouts and formats

### Process
1. **File Processing**: Extract text from PDF/DOCX
2. **Claude Analysis**: Parse content into structured JSON
3. **Data Storage**: Save parsed content to `parsed_content` field
4. **Status Tracking**: Update `parsing_status` and `parsed_at`

### Parsed Content Structure
```json
{
  "personal_info": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "location": "City, State",
    "linkedin": "linkedin.com/in/johndoe"
  },
  "professional_summary": "Brief professional overview...",
  "experience": [
    {
      "title": "Software Engineer",
      "company": "Tech Corp",
      "location": "City, State",
      "start_date": "2020-01",
      "end_date": "2023-12",
      "description": "Job responsibilities and achievements...",
      "achievements": ["Achievement 1", "Achievement 2"]
    }
  ],
  "education": [
    {
      "degree": "Bachelor of Science",
      "field": "Computer Science",
      "institution": "University Name",
      "graduation_date": "2020-05",
      "gpa": "3.8"
    }
  ],
  "skills": {
    "technical": ["Python", "JavaScript", "React"],
    "soft": ["Leadership", "Communication"],
    "tools": ["Git", "Docker", "AWS"]
  },
  "certifications": [
    {
      "name": "AWS Certified Developer",
      "issuer": "Amazon",
      "date": "2023-06",
      "expiry": "2026-06"
    }
  ],
  "projects": [
    {
      "name": "Project Name",
      "description": "Project description...",
      "technologies": ["React", "Node.js"],
      "url": "github.com/user/project"
    }
  ],
  "languages": [
    {
      "language": "English",
      "proficiency": "Native"
    }
  ],
  "additional_sections": {
    "volunteer_work": [],
    "publications": [],
    "awards": []
  }
}
```

### Parsing Status Values
- `pending`: Resume uploaded, parsing not started
- `parsing`: Claude is currently parsing the resume
- `parsed`: Parsing completed successfully
- `failed`: Parsing failed (error stored in `parsing_error`)

## Stage 2: Resume Scoring (OpenAI + Claude)

### Purpose
- Analyze parsed content for quality and ATS compatibility
- Generate detailed scores across multiple categories
- Provide actionable improvement suggestions

### Process
1. **Input Preparation**: Use `parsed_content` as structured input
2. **Claude Format Analysis**: Analyze structure and ATS compatibility (25%)
3. **OpenAI Content Analysis**: Evaluate content across 5 categories (75%)
4. **Score Calculation**: Compute weighted overall score
5. **Feedback Generation**: Create improvement suggestions

### Scoring Categories

#### Claude Analysis (25% of overall score)
- **Format & Structure**: ATS compatibility, layout, formatting
- **Section Organization**: Proper headings, logical flow
- **Contact Information**: Completeness and placement
- **Date Formatting**: Consistency and clarity

#### OpenAI Analysis (75% of overall score)
- **Career Overview (25%)**: Professional summary and skills
- **Experience (40%)**: Work history, achievements, progression
- **Education (15%)**: Degrees, certifications, relevance
- **Additional Qualifications (10%)**: Projects, languages, extras
- **Content Quality (10%)**: Grammar, clarity, professionalism

### Scoring Status Values
- `pending`: Parsing completed, scoring not started
- `scoring`: AI models are analyzing the content
- `scored`: Scoring completed successfully
- `failed`: Scoring failed (error stored in `scoring_error`)

## Database Schema

### Enhanced Resumes Table
```sql
-- Parsing fields
parsed_content JSONB,                    -- Structured content from Claude
parsing_status TEXT DEFAULT 'pending',   -- parsing, parsed, failed
parsed_at TIMESTAMP,                     -- When parsing completed
parsing_error TEXT,                      -- Error message if failed

-- Scoring fields  
overall_score DECIMAL(5,2),              -- Final weighted score (0-100)
claude_score DECIMAL(5,2),               -- Format analysis score
openai_score DECIMAL(5,2),               -- Content analysis score
career_overview_score DECIMAL(5,2),     -- Career section score
experience_score DECIMAL(5,2),          -- Experience section score
education_score DECIMAL(5,2),           -- Education section score
additional_qualifications_score DECIMAL(5,2), -- Additional quals score
content_quality_score DECIMAL(5,2),     -- Content quality score
scoring_feedback JSONB,                 -- Detailed feedback
scoring_status TEXT DEFAULT 'pending',   -- scoring, scored, failed
last_scored_at TIMESTAMP,               -- When scoring completed
scoring_error TEXT                      -- Error message if failed
```

### Scoring History Table
Tracks both parsing and scoring changes over time for improvement analysis.

## API Workflow

### 1. Resume Upload
```
POST /api/resumes/upload
→ File stored in Supabase Storage
→ Resume record created with parsing_status='pending'
→ Parsing task queued
```

### 2. Parsing Process
```
Celery Task: parse_resume_task
→ Extract text from file
→ Call Claude API for parsing
→ Update parsed_content and parsing_status
→ Queue scoring task if successful
```

### 3. Scoring Process
```
Celery Task: score_resume_task
→ Use parsed_content as input
→ Call Claude for format analysis
→ Call OpenAI for content analysis
→ Calculate weighted scores
→ Update scoring fields and status
```

### 4. Results Retrieval
```
GET /api/resumes/{id}
→ Returns complete resume data including:
  - parsed_content (structured data)
  - all scores and feedback
  - parsing/scoring status
```

## Error Handling

### Parsing Errors
- File corruption or unsupported format
- Claude API failures or rate limits
- Text extraction issues

### Scoring Errors
- Missing parsed content
- OpenAI/Claude API failures
- Invalid score calculations

### Recovery Mechanisms
- Automatic retries with exponential backoff
- Fallback to alternative parsing methods
- Graceful degradation for partial failures

## Performance Considerations

### Optimization Strategies
- **Parallel Processing**: Parse and score different resumes simultaneously
- **Caching**: Cache parsed content to avoid re-parsing
- **Batch Processing**: Group similar operations
- **Rate Limiting**: Respect AI service limits

### Monitoring
- Track parsing/scoring success rates
- Monitor API response times
- Alert on high failure rates
- Cost tracking for AI services

## Security & Privacy

### Data Protection
- Parsed content contains sensitive personal information
- RLS policies ensure user data isolation
- Automatic cleanup of old parsing data
- Secure API key management

### Compliance
- GDPR compliance for EU users
- Data retention policies
- User consent for AI processing
- Audit trails for data access

## Future Enhancements

### Planned Improvements
- **Multi-language Support**: Parse resumes in different languages
- **Industry-specific Parsing**: Tailored parsing for different fields
- **Real-time Processing**: Faster parsing and scoring
- **Advanced Analytics**: Trend analysis and benchmarking

This workflow ensures high-quality, consistent resume analysis while maintaining performance and security standards.
